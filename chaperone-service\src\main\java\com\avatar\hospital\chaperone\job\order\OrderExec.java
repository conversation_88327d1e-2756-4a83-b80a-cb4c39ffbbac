package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.repository.OrderRepositoryService;
import com.avatar.hospital.chaperone.request.order.OrderRequest;
import com.avatar.hospital.chaperone.service.order.OrderService;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-11-09 17:07
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderExec {
    private final OrderRepositoryService orderRepositoryService;
    private final OrderService orderService;

    /**
     * 订单过期取消
     *
     * @param dateHour
     * @return
     */
    public Boolean orderExpireCancel(Integer dateHour) {
        // 查询所有未过期的订单
        long start = System.currentTimeMillis();
        log.info("OrderExec[]orderExpireCancel start>> dateHour:{}", dateHour);
        List<OrderDO> orderList = orderRepositoryService.findAllExpire(dateHour);
        if (CollUtils.isEmpty(orderList)) {
            log.info("OrderExec[]orderExpireCancel orderList is null");
            return Boolean.TRUE;
        }
        for (OrderDO order : orderList) {
            ExceptionUtil.execute(log, () -> {
                OrderRequest request = OrderRequest.buildForCancelById(order.getId());
                orderService.cancel(request);
                return Boolean.TRUE;
            });
        }
        log.info("OrderExec[]orderExpireCancel end,cost:{}", System.currentTimeMillis() - start);
        return Boolean.TRUE;
    }
}
