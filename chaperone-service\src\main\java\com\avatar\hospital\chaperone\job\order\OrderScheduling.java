package com.avatar.hospital.chaperone.job.order;

import com.avatar.hospital.chaperone.annotation.Task;
import com.avatar.hospital.chaperone.component.wx.WxTemplateMessageSendComponent;
import com.avatar.hospital.chaperone.component.wx.WxUserSyncComponent;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderPayDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderBillSubType;
import com.avatar.hospital.chaperone.database.order.enums.OrderPayStatus;
import com.avatar.hospital.chaperone.database.order.enums.OrderTradeType;
import com.avatar.hospital.chaperone.database.order.repository.OrderPayRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderRepositoryService;
import com.avatar.hospital.chaperone.job.ScheduledHelper;
import com.avatar.hospital.chaperone.job.ScheduledType;
import com.avatar.hospital.chaperone.properties.WxTemplateMessageBizProperties;
import com.avatar.hospital.chaperone.properties.WxTemplateMessageProperties;
import com.avatar.hospital.chaperone.service.order.OrderBillService;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * order 定时任务
 *
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-17 15:02
 **/
@Task(value = "OrderScheduling", description = "订单定时任务描述")
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderScheduling {
    private final OrderConsumerLogExec orderConsumerLogExec;
    private final OrderBillExec orderBillExec;
    private final OrderExec orderExec;
    private final ScheduledHelper scheduledHelper;
    private final NursingOrderDayExec nursingOrderDayExec;

    private final OrderPayRepositoryService orderPayRepositoryService;
    private final OrderRepositoryService orderRepositoryService;
    private final OrderBillService orderBillService;
    private final WxTemplateMessageSendComponent wxTemplateMessageSendComponent;
    private final WxTemplateMessageBizProperties wxTemplateMessageBizProperties;
    private final WxUserSyncComponent wxUserSyncComponent;


    // 消耗统计

    /**
     * 根据套餐配置,检测是否需要生成一条记录
     * 每个小时执行一次
     */
    @Scheduled(cron = "3 0 * * * ?")
    public void consumerLogByEveryHour() {
        Integer hour = DateUtils.hour();
        Integer date = DateUtils.dateInt();
        scheduledHelper.exec(ScheduledType.CONSUMER_LOG_BY_EVERY_HOUR, () -> consumerLog(date, hour));
    }

    /**
     * 订单消耗记录
     * 生成的消费记录表为明天
     *
     * @return
     */
    @Task(value = "order-consumerlog", description = "订单-消费记录生成(每小时执行一次)(20231025,1 => 开始时间日期,小时)")
    public Boolean consumerLog(Integer date, Integer hour) {
        return orderConsumerLogExec.consumerLogByEveryHour(date, hour);
    }

    /**
     * 订单护工预设
     *
     * @return
     */
    @Task(value = "order-reset-order-nursing", description = "订单-护工预设(每小时执行一次)(2023102501 => 时间日期加小时)")
    public Boolean resetOrderNursingByPre(Integer dateH) {
        orderConsumerLogExec.resetOrderNursingByPre(dateH);
        return Boolean.TRUE;
    }

    // 结算单

    /**
     * 创建结算单
     * 每个小时跑一次,看有哪些单子是可以自动提交结算了
     */
    @Scheduled(cron = "0 3 * * * ?")
    public void createSettleByEveryHour() {
        Integer dateHour = DateUtils.dateHourInt();
        scheduledHelper.exec(ScheduledType.SETTLE_BILL_BY_EVERY_HOUR, () -> orderSettle(dateHour));
    }

    /**
     * 订单结算单
     *
     * @return
     */
    @Task(value = "order-settle", description = "订单-结算单生产(每小时执行一次)(2023102501 => 时间日期加小时)")
    public Boolean orderSettle(Integer dateHour) {
        return orderBillExec.createSettle(dateHour);
    }

    /**
     * 订单过期取消
     * 每个小时跑一次,看有哪些单子是可以自动提交结算了
     */
    @Scheduled(cron = "0 3 * * * ?")
    public void orderExpireCancelByEveryHour() {
        Integer dateHour = DateUtils.dateHourInt();
        scheduledHelper.exec(ScheduledType.CONSUMER_LOG_BY_EVERY_HOUR, () -> orderExpireCancel(dateHour));
    }

    /**
     * 订单取消
     *
     * @return
     */
    @Task(value = "order-expire-cancel", description = "订单-过期取消(每小时执行一次)(2023102501 => 时间日期加小时)")
    public Boolean orderExpireCancel(Integer dateHour) {
        return orderExec.orderExpireCancel(dateHour);
    }

    // 账单

    /**
     * 周期账单-月
     * 每个月1号执行一次
     */
    @Scheduled(cron = "0 1 0 1 * ?")
    public void orderBillByEveryMonth() {
        final Integer startDate = DateUtils.lastMonthStart();
        final Integer endDate = DateUtils.lastMonthEnd();
        scheduledHelper.exec(ScheduledType.ORDER_BILL_BY_EVERY_MONTH, () -> orderBillByTime(OrderBillSubType.MONTH.getStatus(), startDate, endDate));
    }

    /**
     * 季度账单-季度
     * 每个季度执行一次
     */
    @Scheduled(cron = "0 2 0 1 4,7,10,1 ?")
    public void orderBillByEverySeason() {
        final Integer startDate = DateUtils.lastSeasonStart();
        final Integer endDate = DateUtils.lastSeasonEnd();
        scheduledHelper.exec(ScheduledType.ORDER_BILL_BY_EVERY_MONTH, () -> orderBillByTime(OrderBillSubType.SEASON.getStatus(), startDate, endDate));
    }

    /**
     * 季度账单-年度
     * 每年1月1号执行一次
     */
    @Scheduled(cron = "0 3 0 1 1 ?")
    public void orderBillByEveryYear() {
        final Integer startDate = DateUtils.lastYearStart();
        final Integer endDate = DateUtils.lastMonthEnd();
        scheduledHelper.exec(ScheduledType.ORDER_BILL_BY_EVERY_MONTH, () -> orderBillByTime(OrderBillSubType.YEAR.getStatus(), startDate, endDate));
    }

    /**
     * 创建周期账单
     *
     * @param billSubType
     * @param startTime
     * @param endTime
     * @return
     */
    @Task(value = "order-bill", description = "订单-账单生成(11,20231025,20231125 => 周期账单类型,开始日期,结束日期 11月度,12季度，13年度)")
    public Boolean orderBillByTime(Integer billSubType, Integer startTime, Integer endTime) {
        OrderBillSubType subType = OrderBillSubType.of(billSubType);
        return orderBillExec.createCycle(subType, startTime, endTime);
    }

    // 护工订单排班

    /**
     * 护工根据订单情况添加考勤排班记录
     */
    @Scheduled(cron = "0 3 0 25 * ?")
    public void orderNursingEveryMonth() {
        final Integer startDate = DateUtils.nextMonthStart();
        final Integer endDate = DateUtils.nextMonthEnd();
        scheduledHelper.exec(ScheduledType.ORDER_NURSING_BY_EVERY_MONTH, () -> orderNursing(null, startDate, endDate));
    }

    /**
     * 护工排班
     *
     * @param nursingId 护工ID
     * @param startTime yyyyMMdd
     * @param endTime   yyyyMMdd
     * @return
     */
    @Task(value = "order-nursing", description = "订单-护工排班(null,20231025,20231030 => 护工ID,开始时间,结束时间)")
    public Boolean orderNursing(Long nursingId, Integer startTime, Integer endTime) {
        return nursingOrderDayExec.add(nursingId, startTime, endTime);
    }

    // 微信支付查询

    /**
     * 微信支付查询
     */
    @Scheduled(cron = "30 * * * * ?")
    public void orderPayStatusForMinutes() {
        scheduledHelper.exec(ScheduledType.ORDER_PAY_BY_EVERY_MINUTES, () -> orderPayStatusForMinutes(null));
    }

    /**
     * 订单-支付(微信)状态查询同步
     * 最新一个小时内的订单信息
     *
     * @param payId 护工ID
     * @return
     */
    @Task(value = "order-pay", description = "订单-支付(微信)状态查询同步(257411237024866304 = 支付单ID t_order_pay.id)")
    public Boolean orderPayStatusForMinutes(Long payId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = now.plusMinutes(-1);
        LocalDateTime startTime = now.plusHours(-6);
        int pageIndex = 1;
        int pageSize = 100;

        LambdaQueryWrapper<OrderPayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPayDO::getDeleted, DelUtils.NO_DELETED);
        if (Objects.isNull(payId)) {
            queryWrapper.eq(OrderPayDO::getTradeType, OrderTradeType.WECHAT.getStatus());
            queryWrapper.eq(OrderPayDO::getPayStatus, OrderPayStatus.PAYING.getStatus());
            queryWrapper.ge(OrderPayDO::getCreatedAt, startTime);
            queryWrapper.le(OrderPayDO::getCreatedAt, endTime);
        } else {
            queryWrapper.eq(OrderPayDO::getId, payId);
        }
        List<OrderPayDO> records = null;
        do {
            Page<OrderPayDO> page = PageDTO.of(pageIndex, pageSize);
            page = orderPayRepositoryService.page(page, queryWrapper);
            records = page.getRecords();
            for (OrderPayDO orderPayDO : records) {
                ExceptionUtil.execute(log, () -> orderBillService.payUpdateByQuery(orderPayDO));
            }
            pageIndex++;
        } while (records.size() >= pageSize);
        return Boolean.TRUE;
    }


    // 即将逾期订单提醒

    /**
     * 即将逾期订单提醒
     */
    @Scheduled(cron = "1 0 15 * * ?")
    public void orderPayMessageByEveryDay() {
        scheduledHelper.exec(ScheduledType.ORDER_PAY_MESSAGE_BY_EVERY_DAY, () -> orderPayMessageByEveryDay(null));
    }

    /**
     * 针对病人的服务套餐到期前一天，下午15点提醒用户进行付费，付费提醒文字模板等由瑞新物业提供
     *
     * @param orderId 护工ID
     * @return
     */
    @Task(value = "order-pay-message", description = "针对病人的服务套餐到期前一天，下午15点提醒用户进行付费，付费提醒文字模板等由瑞新物业提供(257411237024866304 = 支付单ID)")
    public Boolean orderPayMessageByEveryDay(Long orderId) {
        LocalDateTime startTime = LocalDateTime.now().withHour(15).withMinute(0).withSecond(0); // 今天15点之后,不包括
        LocalDateTime endTime = LocalDateTime.now().plusDays(1).withHour(15).withMinute(0).withSecond(0); // 明天15点之前,包括15点

        int pageIndex = 1;
        int pageSize = 100;

        LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDO::getDeleted, DelUtils.NO_DELETED);
        queryWrapper.gt(Objects.isNull(orderId), OrderDO::getRealEndTime, DateUtils.dateIntH(startTime));
        queryWrapper.le(Objects.isNull(orderId), OrderDO::getRealEndTime, DateUtils.dateIntH(endTime));
        queryWrapper.eq(Objects.nonNull(orderId), OrderDO::getId, orderId);
        List<OrderDO> records = null;
        do {
            Page<OrderDO> page = PageDTO.of(pageIndex, pageSize);
            page = orderRepositoryService.page(page, queryWrapper);
            records = page.getRecords();
            for (OrderDO order : records) {
                ExceptionUtil.execute(log, () -> orderPayMessageByEveryDay0(order));
            }
            pageIndex++;
        } while (records.size() >= pageSize);
        return Boolean.TRUE;
    }

    // 微信公众用户同步

    /**
     * 微信公众用户同步
     */
    @Scheduled(cron = "1 0 3,13 * * ? ")
    public void orderPayUserSyncByEveryDay() {
        scheduledHelper.exec(ScheduledType.ORDER_PAY_USER_SYNC_EVERY_DAY, () -> wxUserSync());
    }


    @Task(value = "wx-user-sync", description = "微信公众号用户同步")
    public Boolean wxUserSync() {
        wxUserSyncComponent.sync();
        return Boolean.TRUE;
    }

    public Integer orderPayMessageByEveryDay0(OrderDO order) {
        String orderPayTemplateId = wxTemplateMessageBizProperties.getOrderPayTemplateId();
        log.info("OrderScheduling[]orderPayMessageByEveryDay0 >> orderPayTemplateId:{}", orderPayTemplateId);
        List<WxTemplateMessageProperties.TemplateData> data = new ArrayList<>();
        data.add(WxTemplateMessageProperties.TemplateData.build("thing4", order.getPatientName()));
        data.add(WxTemplateMessageProperties.TemplateData.build("thing3", "未缴费"));
        data.add(WxTemplateMessageProperties.TemplateData.build("time7", DateUtils.parseDateForStrH(order.getRealEndTime())));
        wxTemplateMessageSendComponent.sendMessage(order.getAccountId(), orderPayTemplateId, data);
        return 1;
    }
}
