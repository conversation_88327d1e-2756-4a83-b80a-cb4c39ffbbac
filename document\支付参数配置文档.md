# 医院陪护系统支付参数配置文档

## 概述

本文档详细说明了医院陪护系统中订单支付功能的参数配置方法，包括微信支付的配置、证书设置、回调地址配置等。

## 支付流程概述

系统支付流程如下：
1. 用户发起支付请求 (`/api/v1/consumer/order/bill/pay`)
2. 系统创建支付订单记录
3. 调用微信支付统一下单接口
4. 返回支付参数给前端
5. 用户完成支付后，微信回调通知系统
6. 系统更新订单支付状态

## 核心配置参数

### 1. 微信支付基础配置

在各环境的 `application-{env}.yaml` 文件中配置：

```yaml
wx:
  pay:
    appId: "微信小程序AppID"           # 微信小程序的AppID
    mchId: 商户号                      # 微信支付商户号
    mchKey: "商户密钥"                 # 微信支付商户密钥
    certSerialNo: "证书序列号"         # API证书序列号
    privateKeyPath: "classpath:cert/apiclient_key.pem"      # 私钥证书路径
    privateCertPath: "classpath:cert/apiclient_wx_cert.pem" # 公钥证书路径
    notifyUrl: "支付回调地址"          # 支付成功回调通知地址
```

### 2. 不同环境配置示例

#### 开发环境 (dev)
```yaml
wx:
  pay:
    appId: "wx857af75a951372a8"
    mchId: 1533433711
    mchKey: wJ65GOBWhqCXc406PJF6XQth7VqWZ18j
    certSerialNo: 3C456C7350309A2C22D3261A5896878DB0C0417E
    privateKeyPath: classpath:cert/apiclient_key.pem
    privateCertPath: classpath:cert/apiclient_wx_cert.pem
    notifyUrl: 'https://localhsot:8080/api/v1/consumer/notify/wx'
```

#### UAT环境 (uat)
```yaml
wx:
  pay:
    appId: "wxa0360fe3b2279192"
    mchId: 1656539250
    mchKey: zhejiangriseholdinggrouppay13579
    certSerialNo: 3C456C7350309A2C22D3261A5896878DB0C0417E
    privateKeyPath: classpath:cert/apiclient_key.pem
    privateCertPath: classpath:cert/apiclient_wx_cert.pem
    notifyUrl: 'https://hospital-chaperone-consumer.ruixin-sit.supaur.tech/api/v1/consumer/notify/wx'
```

#### 生产环境 (prod)
```yaml
wx:
  pay:
    appId: "wxa0360fe3b2279192"
    mchId: 1656539250
    mchKey: zhejiangriseholdinggrouppay13579
    certSerialNo: 3C456C7350309A2C22D3261A5896878DB0C0417E
    privateKeyPath: classpath:cert/apiclient_key.pem
    privateCertPath: classpath:cert/apiclient_wx_cert.pem
    notifyUrl: 'https://comsumer-backend.rxwysystem.com/api/v1/consumer/notify/wx'
```

## 证书配置

### 1. 证书文件位置
证书文件需要放置在项目的 `src/main/resources/cert/` 目录下：
- `apiclient_key.pem` - 商户私钥证书
- `apiclient_wx_cert.pem` - 商户公钥证书

### 2. 证书获取方式
1. 登录微信商户平台
2. 进入"账户中心" -> "API安全"
3. 下载API证书
4. 将证书文件放置到指定目录

## 支付接口说明

### 1. 发起支付接口
- **接口路径**: `/api/v1/consumer/order/bill/pay`
- **请求方法**: POST
- **功能**: 创建支付订单并调用微信支付

#### 请求参数 (OrderBillPayRequest)
```json
{
  "billId": 123456789,  // 账单ID，必填
  "operatorUser": {     // 操作用户信息
    "id": 987654321,
    "name": "用户姓名"
  }
}
```

#### 响应参数 (OrderBillPayResponse)
```json
{
  "code": "200",
  "message": "success",
  "data": {
    "id": 123456789,           // 支付订单ID
    "orderId": 987654321,      // 订单ID
    "billId": 111222333,       // 账单ID
    "outTradeNo": "wx_pay_123", // 微信订单号
    "tradeType": 1,            // 交易类型
    "price": 10000,            // 支付金额(分)
    "payTime": "2023-10-17 10:30:00", // 支付时间
    "payInfo": {               // 微信支付参数
      "appId": "wx857af75a951372a8",
      "timeStamp": "1697515800",
      "nonceStr": "randomstring",
      "package": "prepay_id=wx_prepay_123",
      "signType": "MD5",
      "paySign": "signature_string"
    }
  }
}
```

### 2. 支付回调接口
- **接口路径**: `/api/v1/consumer/notify/wx`
- **请求方法**: POST
- **功能**: 接收微信支付成功通知
- **说明**: 此接口由微信服务器调用，无需前端直接调用

## 支付状态说明

### 支付订单状态枚举 (OrderPayStatus)
```java
TIME_OUT(-4, "已过期")      // 支付超时
FAIL(-3, "支付失败")        // 支付失败
CANCEL(-2, "取消")          // 支付取消
NONE(-1, "未知")            // 未知状态
UNPAY(0, "未支付")          // 未支付，对应微信"NOTPAY"
PAYING(1, "待支付")         // 支付中
SUCCESS(2, "支付成功")      // 支付成功，对应微信"SUCCESS"
CLOSE(3, "订单已关闭")      // 订单关闭，对应微信"CLOSED"
REFUND_ALL(10, "全部退款")  // 全额退款，对应微信"REFUND"
REFUND_SUB(11, "部分退款")  // 部分退款，对应微信"REFUND"
```

### 订单状态枚举 (OrderStatus)
```java
CANCEL(1, "取消")                           // 订单取消
CLOSE(2, "已关闭")                          // 订单关闭
FINISH(3, "已完结")                         // 订单完结
COMMIT_HOSPITAL(10, "待院方审核")           // 等待医院审核
COMMIT_CERTIFIED_PROPERTY(11, "待物业确认") // 等待物业确认
COMMIT_COMPLETE_INFO(12, "待完善信息")      // 等待完善信息
WAIT_CONFIRM(21, "待用户确认下单")          // 等待用户确认
WORK(30, "陪护中")                          // 陪护进行中
APPLY_SETTLE(31, "提交结算申请")            // 申请结算
```

## 错误码说明

### 支付相关错误码
```java
ORDER_BILL_NOT_EXIST("账单不存在")
ORDER_BILL_ALREADY_PAY_ERROR("账单已支付")
ORDER_PAY_CREATE_ERROR("支付单创建失败,请稍后重试!")
ORDER_PAY_NOT_EXIST("支付单不存在")
CONSUMER_ACCOUNT_PAY_OPENID_ERROR("支付openid未绑定")
PARAMETER_ERROR("参数错误")
SYSTEM_ERROR("系统异常")
IDEMPOTENT_ERROR("请勿重复提交")
```

## 关键代码组件

### 1. WxPayComponent
位置：`chaperone-service/src/main/java/com/avatar/hospital/chaperone/component/wx/WxPayComponent.java`

主要功能：
- 微信支付统一下单
- 支付结果查询
- 退款处理
- 支付回调处理

### 2. OrderBillServiceImpl
位置：`chaperone-service/src/main/java/com/avatar/hospital/chaperone/service/order/impl/OrderBillServiceImpl.java`

主要功能：
- 支付订单创建
- 支付参数构建
- 支付状态更新

## 快速配置指南

### 第一步：获取微信支付参数
1. 登录[微信商户平台](https://pay.weixin.qq.com/)
2. 获取以下信息：
   - 商户号 (mchId)
   - 商户密钥 (mchKey) - 在"账户中心 > API安全"中设置
   - AppID - 关联的微信小程序AppID

### 第二步：下载API证书
1. 在微信商户平台进入"账户中心 > API安全"
2. 下载API证书，获得以下文件：
   - `apiclient_key.pem` (商户私钥)
   - `apiclient_cert.pem` (商户证书)
3. 查看证书序列号：
   ```bash
   openssl x509 -in apiclient_cert.pem -serial -noout
   ```

### 第三步：配置项目参数
1. 将证书文件放置到 `src/main/resources/cert/` 目录
2. 修改对应环境的配置文件 `application-{env}.yaml`：
   ```yaml
   wx:
     pay:
       appId: "你的微信小程序AppID"
       mchId: 你的商户号
       mchKey: "你的商户密钥"
       certSerialNo: "你的证书序列号"
       privateKeyPath: "classpath:cert/apiclient_key.pem"
       privateCertPath: "classpath:cert/apiclient_cert.pem"
       notifyUrl: "https://你的域名/api/v1/consumer/notify/wx"
   ```

### 第四步：配置回调地址
1. 在微信商户平台进入"产品中心 > 开发配置"
2. 设置支付回调URL：`https://你的域名/api/v1/consumer/notify/wx`
3. 确保回调地址使用HTTPS协议且可被外网访问

### 第五步：测试验证
1. 启动应用服务
2. 调用支付接口进行测试
3. 检查日志确认配置正确
4. 进行小额支付测试验证

## 详细配置步骤

### 1. 环境变量配置
根据部署环境选择对应的配置文件，确保以下参数正确：
- 微信小程序AppID
- 商户号和商户密钥
- 证书序列号
- 回调地址

### 2. 证书部署
1. 将微信支付证书文件上传到服务器
2. 确保证书文件路径与配置中的路径一致
3. 验证证书文件权限

### 3. 回调地址配置
1. 在微信商户平台配置支付回调地址
2. 确保回调地址可以被微信服务器访问
3. 回调地址格式：`https://域名/api/v1/consumer/notify/wx`

## 注意事项

1. **证书安全**: 证书文件包含敏感信息，需要妥善保管，不要提交到代码仓库
2. **环境隔离**: 不同环境使用不同的商户号和证书
3. **回调地址**: 回调地址必须是HTTPS协议，且能被微信服务器访问
4. **商户密钥**: 商户密钥长度为32位，需要在微信商户平台设置
5. **证书更新**: 证书有效期通常为1年，需要定期更新

## 常见问题与故障排查

### 1. 支付失败问题
**现象**: 调用支付接口返回失败或异常

**排查步骤**:
1. 检查配置参数
   ```bash
   # 验证配置文件中的参数
   grep -A 10 "wx:" application-{env}.yaml
   ```
2. 验证证书文件
   ```bash
   # 检查证书文件是否存在
   ls -la src/main/resources/cert/
   # 验证证书格式
   openssl x509 -in apiclient_wx_cert.pem -text -noout
   ```
3. 查看详细错误日志
   ```bash
   # 查看支付组件日志
   grep "WxPayComponent" logs/application.log
   ```

**常见错误**:
- `商户号不存在`: 检查mchId配置
- `签名错误`: 检查mchKey和证书配置
- `AppID不匹配`: 检查appId配置

### 2. 回调处理失败
**现象**: 支付成功但订单状态未更新

**排查步骤**:
1. 检查回调地址配置
   ```yaml
   wx:
     pay:
       notifyUrl: 'https://域名/api/v1/consumer/notify/wx'
   ```
2. 验证回调地址可访问性
   ```bash
   curl -X POST https://域名/api/v1/consumer/notify/wx
   ```
3. 查看回调处理日志
   ```bash
   grep "payUpdateByNotifyWx" logs/application.log
   ```

**解决方案**:
- 确保回调地址使用HTTPS协议
- 检查防火墙和负载均衡配置
- 验证证书配置正确

### 3. 证书相关错误
**现象**: 证书验证失败或证书过期

**排查步骤**:
1. 检查证书序列号
   ```bash
   # 查看证书序列号
   openssl x509 -in apiclient_wx_cert.pem -serial -noout
   ```
2. 验证证书有效期
   ```bash
   # 查看证书有效期
   openssl x509 -in apiclient_wx_cert.pem -dates -noout
   ```
3. 检查证书文件权限
   ```bash
   ls -la src/main/resources/cert/
   ```

**解决方案**:
- 更新证书序列号配置
- 重新下载最新证书文件
- 确保证书文件可读权限

### 4. 支付金额异常
**现象**: 支付金额不正确或为0

**排查步骤**:
1. 检查账单金额计算逻辑
   ```java
   Integer price = orderBillDO.getPriceReceivable() - orderBillDO.getPriceReceived();
   ```
2. 验证数据库中的金额字段
3. 查看价格计算日志

**解决方案**:
- 确保金额单位为分(微信支付要求)
- 检查账单状态和已支付金额
- 验证价格计算逻辑

### 5. OpenID获取失败
**现象**: 提示"支付openid未绑定"

**排查步骤**:
1. 检查用户OpenID绑定状态
   ```sql
   SELECT * FROM account_open_id WHERE account_id = ? AND app_id = ?;
   ```
2. 验证微信小程序配置
3. 检查用户授权流程

**解决方案**:
- 确保用户已完成微信授权
- 检查OpenID获取和存储逻辑
- 验证appId配置正确

## 测试验证

1. **支付测试**: 使用测试环境进行小额支付测试
2. **回调测试**: 验证支付成功后回调处理是否正常
3. **退款测试**: 测试退款功能是否正常工作

## 技术实现细节

### 1. 支付订单创建流程
```java
// 1. 验证账单状态和金额
OrderBillDO orderBillDO = orderBillRepositoryService.getById(request.getBillId());
Integer price = orderBillDO.getPriceReceivable() - orderBillDO.getPriceReceived();

// 2. 获取用户OpenID
AccountOpenIdDO accountOpenId = accountOpenIdRepositoryService.findByAccountIdAndAppId(operator,payAppId);

// 3. 创建支付订单记录
OrderPayDO payDO = OrderBuilder.createOrderPayDOByPay(request,price,orderBillDO.getOrderId());
orderPayRepositoryService.save(payDO);

// 4. 构建微信支付请求
WxPayUnifiedOrderRequest payRequest = toWxPayUnifiedOrderRequest(accountOpenId.getOpenId(),payDO,request.getIp());

// 5. 调用微信支付接口
WxPayUnifiedOrderResult payResult = wxPayComponent.pay(payRequest);
```

### 2. 支付参数构建
```java
public WxPayUnifiedOrderRequest toWxPayUnifiedOrderRequest(String openId, OrderPayDO payDO, String ip) {
    WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
    request.setAppid(payAppId);           // 小程序AppID
    request.setMchId(payMchId);           // 商户号
    request.setBody(OrderConst.PAY_DESCRIPTION);  // 商品描述
    request.setOutTradeNo(payDO.getId().toString()); // 商户订单号
    request.setTotalFee(payDO.getPrice()); // 支付金额(分)
    request.setNotifyUrl(notifyUrl);       // 回调地址
    request.setTradeType("JSAPI");         // 交易类型
    request.setTimeExpire(timeExpire);     // 订单失效时间
    request.setSpbillCreateIp(ip);         // 用户IP
    request.setOpenid(openId);             // 用户OpenID
    return request;
}
```

### 3. 支付回调处理
```java
@Override
public void payUpdateByNotifyWx(String data) {
    // 1. 解析微信回调数据
    WxPayOrderNotifyResult notifyInfo = wxPayComponent.notify(data);

    // 2. 获取分布式锁，防止重复处理
    String lockKey = buildPaySuccessKey(notifyInfo.getOutTradeNo());
    RLock lock = redissonClient.getLock(lockKey);

    try {
        if (lock.tryLock(30, TimeUnit.SECONDS)) {
            // 3. 查询支付订单
            OrderPayDO orderPayDO = orderPayRepositoryService.getById(Long.parseLong(outTradeNo));

            // 4. 处理支付成功逻辑
            paySuccessHandler(orderPayDO, notifyInfo.getOutTradeNo(), notifyInfo.getTimeEnd());
        }
    } finally {
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
```

## 配置文件模板

### application-{env}.yaml 完整模板
```yaml
# 数据库配置
spring:
  datasource:
    url: **************************************************************************************************************************************************
    username: ${DB_MYSQL_USERNAME:chaperone}
    password: ${DB_MYSQL_PASSWORD:123456}

# Redis配置
  redis:
    host: ${DB_REDIS_URL:127.0.0.1}
    port: ${DB_REDIS_PORT:6379}
    database: 0

# 微信配置
wx:
  miniapp:
    appid: "微信小程序AppID"
    secret: "微信小程序Secret"
    configStorage:
      type: RedisTemplate
      keyPrefix: wx-java-wa

  mp:
    appid: "微信公众号AppID"
    secret: "微信公众号Secret"
    configStorage:
      type: RedisTemplate
      keyPrefix: wx-java-wx

  pay:
    appId: "微信支付AppID"
    mchId: 商户号
    mchKey: "商户密钥"
    certSerialNo: "证书序列号"
    privateKeyPath: "classpath:cert/apiclient_key.pem"
    privateCertPath: "classpath:cert/apiclient_wx_cert.pem"
    notifyUrl: "https://域名/api/v1/consumer/notify/wx"
```

## 部署检查清单

### 1. 配置文件检查
- [ ] 微信AppID配置正确
- [ ] 商户号和密钥匹配
- [ ] 证书序列号正确
- [ ] 回调地址可访问
- [ ] 环境变量设置正确

### 2. 证书文件检查
- [ ] 证书文件存在且路径正确
- [ ] 证书文件格式正确(PEM格式)
- [ ] 证书文件权限设置正确
- [ ] 证书未过期

### 3. 网络连接检查
- [ ] 服务器可访问微信支付API
- [ ] 回调地址可被微信服务器访问
- [ ] HTTPS证书有效
- [ ] 防火墙规则配置正确

### 4. 功能测试检查
- [ ] 支付接口调用成功
- [ ] 支付回调处理正常
- [ ] 订单状态更新正确
- [ ] 退款功能正常

## 监控和日志

### 1. 关键日志位置
- 支付请求日志：`WxPayComponent.pay`
- 回调处理日志：`OrderBillServiceImpl.payUpdateByNotifyWx`
- 订单状态更新日志：`paySuccessHandler`

### 2. 监控指标
- 支付成功率
- 回调处理成功率
- 支付订单创建数量
- 异常支付订单数量

## 联系支持

如遇到配置问题，请联系技术支持团队，并提供：
- 具体错误信息
- 环境配置信息
- 相关日志文件
- 支付订单号
