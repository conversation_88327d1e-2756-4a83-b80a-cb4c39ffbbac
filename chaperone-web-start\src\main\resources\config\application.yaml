server:
  port: 8081
  tomcat:
    uri-encoding: UTF-8

spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB
  profiles:
    active: ${ENV:dev}

db:
  mysql: ${DB_MYSQL_URL:127.0.0.1}
  mysql-database: ${DB_MYSQL_DATABASE:chaperone}
  mysql-username: ${DB_MYSQL_USERNAME:chaperone}
  mysql-password: ${DB_MYSQL_PASSWORD:123456}
  redis: ${DB_REDIS_URL:127.0.0.1}
  redis-port: ${DB_REDIS_PORT:6379}

role:
  hospitalListRoleKey: hospitalList
  departmentListRoleKey: departmentList
  organizationTreeRoleKey: organizationTree
  mkdirRoleKey: knowledgeBaseMkdir
