#!/bin/bash

SERVICE_NAME="chaperone-consumer-start-1.0.0-SNAPSHOT.jar"
SERVER_PATH="./$SERVICE_NAME"  # 修正了变量赋值语法

# 1. 停止旧服务
pids=$(pgrep -f "$SERVICE_NAME")  # 使用-f因为jar文件需要全路径匹配

if [ -n "$pids" ]; then
  echo "正在终止进程 $pids"
  kill -15 $pids  # 先尝试SIGTERM（15）
  sleep 2  # 等待进程清理资源

  # 检查是否有残留进程，强制杀死
  remaining_pids=$(pgrep -f "$SERVICE_NAME")
  if [ -n "$remaining_pids" ]; then
    echo "强制终止进程 $remaining_pids"
    kill -9 $remaining_pids
  fi
else
  echo "未找到运行中的 $SERVICE_NAME"
fi

# 2. 启动新服务
LOG_FILE="./output.log"

if [ -f "$SERVER_PATH" ]; then
  # 启动Java服务应该用java -jar，而不是直接执行jar或tar命令
  nohup java -jar "$SERVER_PATH" --spring.profiles.active=prod > "$LOG_FILE" 2>&1 &
  echo "服务已启动，日志见 $LOG_FILE"
else
  echo "错误：服务文件 $SERVER_PATH 不存在"
  exit 1
fi

# 3. 验证服务是否运行
sleep 3  # 增加等待时间，确保进程启动
if pgrep -f "$SERVER_PATH" >/dev/null; then
  echo "服务启动成功！PID: $(pgrep -f "$SERVER_PATH")"
else
  echo "服务启动失败，请检查日志！"
  exit 1
fi