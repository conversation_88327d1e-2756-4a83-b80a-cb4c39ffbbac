package com.avatar.hospital.chaperone.job.order;

import com.alibaba.fastjson.JSON;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderItemDO;
import com.avatar.hospital.chaperone.database.order.dataobject.OrderPresetValueDO;
import com.avatar.hospital.chaperone.database.order.enums.OrderPresetValueStatus;
import com.avatar.hospital.chaperone.database.order.enums.OrderPresetValueType;
import com.avatar.hospital.chaperone.database.order.repository.OrderItemRepositoryService;
import com.avatar.hospital.chaperone.database.order.repository.OrderPresetValueRepositoryService;
import com.avatar.hospital.chaperone.request.order.*;
import com.avatar.hospital.chaperone.response.item.ItemResponse;
import com.avatar.hospital.chaperone.service.item.ItemService;
import com.avatar.hospital.chaperone.service.order.OrderConsumerlogService;
import com.avatar.hospital.chaperone.service.order.OrderService;
import com.avatar.hospital.chaperone.service.order.consts.OrderConst;
import com.avatar.hospital.chaperone.template.util.StrUtils;
import com.avatar.hospital.chaperone.utils.CollUtils;
import com.avatar.hospital.chaperone.utils.DateUtils;
import com.avatar.hospital.chaperone.utils.DelUtils;
import com.avatar.hospital.chaperone.utils.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @program: hospital-chaperone
 * @description:
 * @author: sp0372
 * @create: 2023-10-17 11:04
 **/
@Slf4j
@Component
public class OrderConsumerLogExec {
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderConsumerlogService orderConsumerlogService;
    @Autowired
    private ItemService itemService;
    @Autowired
    private OrderItemRepositoryService orderItemRepositoryService;
    @Autowired
    private OrderPresetValueRepositoryService orderPresetValueRepositoryService;

    /**
     * 每个小时执行一次,看是否到达消费时间点
     * 如果到,会生成次日的消费记录
     *
     * @param date
     * @param hour
     * @return
     */
    public Boolean consumerLogByEveryHour(Integer date, Integer hour) {
        long totalStart = System.currentTimeMillis();
        log.info("OrderConsumerLogExec 消费记录执行 consumerLogByEveryHour start hour:{}", hour);
        resetOrderNursingByPre(DateUtils.toDateHour(date, hour));
        // 获取当前时间节点的商品消费的商品数据
        List<ItemResponse> itemResponseList = itemService.findAll(hour);
        if (CollectionUtils.isEmpty(itemResponseList)) {
            log.info("OrderConsumerLogExec 消费记录创建 consumerLogByEveryHour end >> itemResponseList is empty");
            return Boolean.TRUE;
        }

        // 查询包含这些商品的所有的订单,且在有效期内所有订单
        List<Long> itemIds = CollUtils.toListLongDistinct(itemResponseList, ItemResponse::getId);
        Integer dateHour = DateUtils.toDateHour(date, hour);
        List<OrderItemDO> orderItemDOList = orderItemRepositoryService.listByConsumerLog(itemIds, dateHour);
        if (CollectionUtils.isEmpty(orderItemDOList)) {
            log.info("OrderConsumerLogExec 消费记录创建 consumerLogByEveryHour end >> orderItemDOList is empty");
            return Boolean.TRUE;
        }

        Map<Long, List<OrderItemDO>> orderListMap = orderItemDOList.stream()
                .collect(Collectors.groupingBy(OrderItemDO::getOrderId));
        LocalDate localDate = DateUtils.parseForInt(date);
        localDate = localDate.plusDays(DateUtils.ADD_ONE_DAY);
        Integer nextDay = DateUtils.dateInt(localDate);

        orderListMap.forEach((orderId, list) -> {
            long start = System.currentTimeMillis();
            ExceptionUtil.execute(log, () -> {
                List<Long> itemIdList = CollUtils.toListLongDistinct(list, OrderItemDO::getItemId);
                OrderConsumeCreateRequest createRequest = new OrderConsumeCreateRequest();
                createRequest.setOrderId(orderId);
                createRequest.setDate(nextDay);
                createRequest.setItemIdList(itemIdList);
                createRequest.setOperatorUser(OrderConst.JOB_OPERATOR);
                orderConsumerlogService.create(createRequest);
                return null;
            });
            log.info("OrderConsumerLogExec 消费记录创建 单条处理 {} cost:{} ms", orderId, System.currentTimeMillis() - start);
        });
        log.info("OrderConsumerLogExec 消费记录创建 consumerLogByEveryHour end cost:{}", System.currentTimeMillis() - totalStart);
        return Boolean.TRUE;
    }


    /**
     * 根据预设置值,重置订单护工
     *
     * @param dateTimeH
     */
    public void resetOrderNursingByPre(Integer dateTimeH) {
        long totalStart = System.currentTimeMillis();
        log.info("OrderConsumerLogExec[]setNursing start>> dateTimeH:{}", dateTimeH);
        List<OrderPresetValueDO> orderPresetValueDOS = litCurTime(dateTimeH);
        if (CollectionUtils.isEmpty(orderPresetValueDOS)) {
            log.info("OrderConsumerLogExec[]setNursing is null");
            return;
        }
        for (OrderPresetValueDO presetValue : orderPresetValueDOS) {
            ExceptionUtil.execute(log, () -> {
                log.info("OrderConsumerLogExec setNursing presetValue:{}", JSON.toJSONString(presetValue));
                orderPresetValueRepositoryService.use(presetValue.getOrderId(), OrderPresetValueType.NURSING.getStatus());

                OrderModifyNursingRequest request = new OrderModifyNursingRequest();
                request.setOperatorUser(OrderConst.JOB_OPERATOR);
                request.setOrderId(presetValue.getOrderId());
                request.setNursingIdList(StrUtils.strToListLong(presetValue.getVal()));
                // 立即
                request.setPresetTime(0L);
                request.setTime(dateTimeH);
                orderService.modifyNursing(request);
                return null;
            });
        }
        log.info("OrderConsumerLogExec setNursing end cost:{}", System.currentTimeMillis() - totalStart);
    }


    private List<OrderPresetValueDO> litCurTime(Integer dateTimeH) {
        LocalDateTime localDateTime = DateUtils.parseForIntH(dateTimeH);
        long milli = localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();

        LambdaQueryWrapper<OrderPresetValueDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPresetValueDO::getDeleted, DelUtils.NO_DELETED);
        queryWrapper.eq(OrderPresetValueDO::getTime, milli);
        queryWrapper.eq(OrderPresetValueDO::getStatus, OrderPresetValueStatus.NO.getStatus());
        queryWrapper.eq(OrderPresetValueDO::getType, OrderPresetValueType.NURSING.getStatus());
        List<OrderPresetValueDO> list = orderPresetValueRepositoryService.list(queryWrapper);
        return CollUtils.isEmpty(list) ? Collections.emptyList() : list;
    }


}
