@echo off
setlocal enabledelayedexpansion

echo.
echo  "开始构建流程..."
echo.

:: 设置环境变量
set MVN_CMD=mvn
set DOCKERFILE=Dockerfile
set IMAGE_NAME=hospital-chaperone-consumer
set VERSION=latest

:: 1. 清理并打包项目
echo [步骤1] 使用Maven构建项目...
%MVN_CMD% clean package -D maven.test.skip=true

if %errorlevel% neq 0 (
    echo 错误: Maven构建失败!
    exit /b 1
)
echo Maven构建成功!
echo.

:: 2. 构建Docker镜像
echo [步骤2] 构建Docker镜像...
docker build -f %DOCKERFILE% -t %IMAGE_NAME%:%VERSION% .

if %errorlevel% neq 0 (
    echo 错误: Docker镜像构建失败!
    exit /b 1
)
echo Docker镜像构建成功!
echo.

:: 3. 可选：推送镜像到仓库
:: echo [步骤3] 推送Docker镜像...
:: docker push %IMAGE_NAME%:%VERSION%
:: echo.

:: 4. 清理中间文件（可选）
:: echo [步骤4] 清理临时文件...
:: del /Q target\*.jar
:: echo.

echo "流程完成! 镜像名称: %IMAGE_NAME%:%VERSION%"
echo.

pause